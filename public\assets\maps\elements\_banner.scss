.banner-style-one {
    background-image: url(../images/banner/01.webp);
    height: 850px;
    position: relative;
    z-index: 1;

    @media #{$large-mobile} {
        height: auto;
        background-position: 18%, 45%;

        &::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            height: 100%;
            width: 100%;
            background: #ffffff73;
            z-index: -1;
        }
    }

    @media #{$small-mobile} {
        height: auto;
        background-position: 18%, 45%;
    }

    .banner-one-inner {
        width: 57%;
        margin-left: auto;
        padding: 160px 0 190px 0;
        position: relative;
        display: block;
        margin-top: 43px;

        @media #{$smlg-device} {
            margin-left: 0;
            width: 100%;
        }

        @media #{$large-mobile} {
            padding: 106px 5px 126px 5px;
        }

        .pre-title {
            color: #1C2539;
            text-transform: uppercase;
            font-size: 16px;
            margin-bottom: 10px;
            font-family: var(--font-secondary);
            letter-spacing: 1px;

            span {
                color: var(--color-primary);
                font-weight: 800;
            }
        }

        .title {
            font-weight: 700;
            line-height: 82px;
            margin-bottom: 23px;
            font-size: 60px;
            color: var(--color-primary);
            line-height: 1.3;

            @media #{$large-mobile} {
                font-size: 36px;
            }

            span {
                font-style: italic;
                color: var(--color-primary);
                font-weight: 400;
                font-size: 70px;
                line-height: 1.3;

                @media #{$large-mobile} {
                    font-size: 36px;
                }
            }
        }

        p.disc.banner-para {
            font-size: 18px;
            line-height: 28px;
            font-weight: 400;
            color: #1C2539;
        }
    }

    .shape-iamge-area {
        @media #{$large-mobile} {
            display: none;
        }

        .one {
            position: absolute;
            right: 0;
            bottom: 0;
            z-index: -1;
        }

        .two {
            position: absolute;
            right: 15%;
            bottom: 55%;
            z-index: -1;
            animation: jump-2 10s linear infinite;
        }
    }
}


.rts-banner-area-two {
    background-image: url(../images/banner/02.webp);
    height: 850px;
    display: flex;
    align-items: center;
    justify-content: center;

    @media #{$large-mobile} {
        height: 580px;
    }

    &.two {
        background-image: url(../images/banner/20.webp);
    }

    &.three {
        background-image: url(../images/banner/21.webp);
    }
}

.banner-inner-two-content {
    text-align: center;
    max-width: 80%;
    margin: auto;

    @media #{$mdsm-layout} {
        max-width: 100%;
    }

    .pre-title {
        margin-bottom: 25px;
        text-transform: uppercase;

        span {
            text-transform: uppercase;
            font-weight: 700;
            letter-spacing: 1px;
        }
    }

    * {
        color: #fff;
    }

    .title {
        font-size: 80px;
        line-height: 1.2;

        @media #{$md-layout} {
            font-size: 50px;
        }

        @media #{$sm-layout} {
            font-size: 50px;
        }

        @media #{$large-mobile} {
            font-size: 36px;
        }
    }

    .rts-btn {
        margin: auto;
    }

    p.disc {
        max-width: 65%;
        margin: auto;
        margin-bottom: 40px;
        font-size: 18px;

        @media #{$sm-layout} {
            max-width: 100%;
        }
    }
}

// banner four
.banner-four-area-main-wrapper {
    clip-path: polygon(0% 0%, 100% 0%, 100% 100%, 81.536% 93.689%, 50.99% 100%, 25.313% 93.689%, 0% 100%, 0% 0%);

    .banner-four-bg {
        background-image: url(../images/banner/03.webp);
        height: 824px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: auto;

        &.two {
            background-image: url(../images/banner/25.webp);
        }

        &.three {
            background-image: url(../images/banner/26.webp);
        }

        @media #{$sm-layout} {
            height: 600px;
        }

        .inner-content-wrapper-four-banner {
            span {
                display: flex;
                justify-content: center;
            }
        }
    }
}

.inner-content-wrapper-four-banner {
    .pre {
        font-weight: 700;
        font-size: 16px;
        line-height: 21px;
        letter-spacing: 0.3em;
        text-transform: uppercase;
        color: #FFFFFF;

        @media #{$large-mobile} {
            text-align: center;
        }
    }

    .title {
        font-weight: 900;
        font-size: 100px;
        text-align: center;
        justify-content: center;
        text-transform: uppercase;
        color: #FFFFFF;
        margin-bottom: 20px;
        margin-top: 25px;
        line-height: 1.3;

        @media #{$laptop-device} {
            font-size: 80px;
        }

        @media #{$smlg-device} {
            font-size: 66px;
        }

        @media #{$mdsm-layout} {
            font-size: 32px;
        }

        span.in {
            -webkit-text-stroke-color: #1C2539;
            -webkit-text-stroke: 1px;
            -webkit-text-fill-color: transparent;
            display: inline;
        }
    }

    p.disc {
        max-width: 60%;
        margin: auto;
        text-align: center;
        margin-bottom: 25px;
        font-weight: 500;
        color: #fff;
        font-size: 18px;
        margin-bottom: 45px;
        line-height: 1.5;

        @media #{$mdsm-layout} {
            max-width: 100%;
        }

        @media #{$large-mobile} {
            font-size: 16px;
            font-weight: 400;
        }
    }

    .button-wrapper {
        justify-content: center;
    }
}

.button-wrapper {
    display: flex;
    align-items: center;
    gap: 25px;
    flex-wrap: wrap;
}

.banner-three-box-clip-area {
    clip-path: polygon(0% 0%, 100% 0%, 100% 85.059%, 83.523% 100%, 16.406% 100%, 0% 84.941%, 0% 0%);
    max-width: 1754px;
    margin: auto;
    height: 850px;
    background: #F2F2F2;
    display: flex;
    align-items: center;
    position: relative;

    @media #{$large-mobile} {
        height: 500px;
    }

    svg {
        height: 500px;
    }

    .right-thumbnail {
        clip-path: polygon(59.225% 100%, 0% 100%, 0% 14.588%, 43.944% 0%, 99.93% 0%, 99.93% 100%, 59.225% 100%);
        position: absolute;
        right: 0;
        top: 0;
        bottom: 0;
        max-width: 709px;
        z-index: -1;

        @media #{$laptop-device} {
            max-width: 550px;
            height: 100%;
        }

        @media #{$smlg-device} {
            max-width: 550px;
            height: 100%;
        }

        @media #{$mdsm-layout} {
            max-width: 100%;
            height: 100%;
        }

        img {
            @media #{$laptop-device} {
                max-width: 550px;
                height: 100%;
                object-fit: cover;
            }

            @media #{$smlg-device} {
                max-width: 550px;
                height: 100%;
                object-fit: cover;
            }

            @media #{$mdsm-layout} {
                max-width: 100%;
                height: 100%;
            }
        }
    }

    .banner-inner-conten {
        .pre {
            color: #5D666F;
            font-size: 22px;
            font-weight: 500;
        }

        .title {
            font-size: 86px;
            font-weight: 700px;
            margin-top: 20px;
            margin-bottom: 25px;
            line-height: 1.2;

            @media #{$laptop-device} {
                font-size: 60px;
            }

            @media #{$smlg-device} {
                font-size: 54px;
            }

            @media #{$mdsm-layout} {
                font-size: 44px;
            }

            @media #{$sm-layout} {
                font-size: 34px;
            }

            span {
                font-style: italic;
                font-weight: 500;
            }
        }

        @media #{$mdsm-layout} {
            padding: 50px;
            background: #fff;
            z-index: 10;
            position: relative;
            border-radius: 6px;
        }

        @media #{$sm-layout} {
            padding: 25px;
            border-radius: 6px;
        }
    }

    .all-shape {
        img {
            position: absolute;

            &.one {
                left: 0;
                top: 0;
                animation: jump-2 5s linear infinite;
            }

            &.two {
                top: 10%;
                left: 37%;
                animation: rotateIt 10s linear infinite;
            }

            &.three {
                bottom: 0;
                left: 40%;
                animation: jump-2 3s linear infinite;
            }
        }
    }
}

.rts-banner-area-two {
    position: relative;

    .shape {
        position: absolute;

        &.shape-one {
            position: absolute;
            right: -100%;
            bottom: 0;
            transition: 1s;

            @media #{$large-mobile} {
                max-width: 220px;
            }
        }

        &.shape-two {
            position: absolute;
            right: 0%;
            top: 35%;
            animation: jump-2 5s linear infinite;
            transform: scale(0);
            transition: .4s;
        }

        &.shape-three {
            position: absolute;
            left: 0%;
            animation: jump-2 8s linear infinite;
            top: 60%;
            transition: .6s;
        }

        &.shape-four {
            position: absolute;
            left: -100%;
            top: 0;
            transition: .7s;
        }
    }
}

.mySwiper-banner-two {
    position: relative;

    .swiper-button-next,
    .swiper-button-prev {
        height: 60px;
        width: 60px;
        border-radius: 50%;
        background: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: .3s;

        @media #{$sm-layout} {
            display: none;
        }

        i {
            font-size: 25px;
            transition: .3s;
            color: #1C2539;
        }

        &::after {
            display: none;
        }

        &:hover {
            background: var(--color-primary);
            transform: scale(1.1);

            i {
                color: #fff;
            }
        }
    }

    .swiper-button-next {
        right: 120px;
    }

    .swiper-button-prev {
        left: 120px;
    }
}

.banner-swiper-two {
    .swiper-slide-active {
        .pre-title {
            animation: fadeInUp 1s;
            animation-delay: 0s;
            display: block;
        }

        .title {
            animation: fadeInUp-small 1.5s;
            animation-delay: 0s;
        }

        p.disc {
            animation: fadeInUp 2.5s;
            animation-delay: 0s;
        }

        .rts-btn {
            animation: fadeInUp 3s;
            animation-delay: 0s;
        }

        .shape-two {
            right: 10%;
        }

        .shape-three {
            left: 16%;
        }

        .shape-one {
            right: 0;
        }

        .shape-four {
            left: 0;
        }
    }
}

.banner-four-area-main-wrapper {
    position: relative;

    .swiper-pagination {
        position: absolute;
        left: auto;
        right: 70px;
        top: 50%;
        transform: translateY(-50%);
        max-width: max-content;
        display: flex;
        flex-direction: column;
        z-index: 10;

        @media #{$sm-layout} {
            display: none;
        }

        .swiper-pagination-bullet {
            background: #fff;
            opacity: .66;
            height: 14px;
            width: 14px;
        }

        .swiper-pagination-bullet-active {
            height: 48px;
            width: 48px;
            background-image: url(../images/gallery/bullet.svg);
            background-color: transparent;
            opacity: 1;
        }
    }
}

.banner-four-area-main-wrapper {
    position: relative;
    z-index: 1;

    .banner-shape-left-right {
        pointer-events: none;

        @media #{$mdsm-layout} {
            display: none;
        }

        .left {
            position: absolute;
            left: 0;
            top: 10%;
            z-index: 10;
        }

        .right {
            position: absolute;
            right: 0;
            top: 13%;
            z-index: 1;
        }
    }
}


.banner-five-content {
    padding-top: 283px;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;

    @media #{$smlg-device} {
        padding-top: 160px;
    }

    @media #{$mdsm-layout} {
        flex-direction: column;
        align-items: flex-start;
        gap: 30px;
    }

    .title-area {
        flex-basis: 54%;

        p.pre {
            font-size: 18px;
            text-transform: uppercase;
            color: #5D666F;
            margin-bottom: 28px;

            span {
                font-weight: 700;
                color: var(--color-primary);
            }
        }

        .title {
            font-size: 86px;
            line-height: 1.1;
            margin-bottom: 0;

            @media #{$laptop-device} {
                font-size: 54px;
            }

            @media #{$smlg-device} {
                font-size: 54px;
            }

            @media #{$large-mobile} {
                font-size: 38px;
            }

            span {
                font-weight: 500;
                font-style: italic;
            }
        }
    }

    .description-area {
        flex-basis: 40%;
        padding-left: 6%;
        margin-bottom: 17px;

        @media #{$mdsm-layout} {
            padding-left: 0;
        }
    }
}

.banner-image-primary-five {
    border-radius: 20px;
    overflow: hidden;
    display: block;

    img {
        transition: .3s;
    }
}

.banner-contact-form-five {
    background: var(--color-primary);
    padding: 30px;
    border-radius: 16px;
    margin-left: -86px;
    position: relative;
    z-index: 30;
    text-align: center;

    @media #{$mdsm-layout} {
        margin-left: 0;
        margin-top: 30px;
    }

    @media #{$small-mobile} {
        padding: 22px;
    }

    .title {
        color: #fff;
        margin-bottom: 26px;
        font-size: 24px;
    }

    input {
        height: 52px;
        background: #363D42;
        margin-bottom: 20px;
        border: none;
        color: #E0E0E0;
        padding: 15px;
    }

    textarea {
        height: 110px;
        background: #363D42;
        margin-bottom: 20px;
        border: none;
        color: #E0E0E0;
        padding: 15px;
    }

    .rts-btn {
        border-radius: 10px !important;
        max-width: 100%;

        &::before {
            background: #0f141f !important;
        }
    }
}

.bg_banner-five {
    background-image: url(../images/banner/06.webp);
    height: 900px;

    @media #{$large-mobile} {
        height: 730px;
    }
}

.rts-banner-area-six {
    display: flex;
    align-items: flex-end;
}

.banner-content-six {
    display: flex;
    align-items: flex-end;
    gap: 210px;
    justify-content: space-between;

    @media #{$smlg-device} {
        gap: 30px;
    }

    @media #{$sm-layout} {
        flex-direction: column;
        align-items: flex-start;
    }

    .left-side {
        max-width: 722px;

        .title {
            font-size: 80px;
            margin-bottom: 30px;

            @media #{$laptop-device} {
                font-size: 60px;
            }

            @media #{$smlg-device} {
                font-size: 60px;
            }

            @media #{$sm-layout} {
                font-size: 42px;

                br {
                    display: none;
                }
            }
        }

        p {
            margin-bottom: 0;
            font-size: 18px;
        }

        * {
            color: #fff;
        }
    }

    .right-stars-area {
        max-width: 357px;

        .title {
            color: #fff;
        }

        p {
            text-transform: uppercase;
            color: #FFFFFF;
            font-size: 20px;
            margin-bottom: 25px;
        }

        .stars-wrapper {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 50px;

            i {
                color: #fff;
            }
        }
    }
}

.bg_banner-seven {
    height: 1076px;
    background-image: url(../images/banner/07.webp);
    display: flex;
    align-items: center;

    @media #{$mdsm-layout} {
        height: 800px;
    }

    @media #{$sm-layout} {
        height: 700px;
    }

    &.two {
        background-image: url(../images/banner/08.webp);
        position: relative;

        &.with-video {
            position: relative;
            z-index: 1;

            &::after {
                position: absolute;
                content: '';
                left: 0;
                height: 100%;
                width: 100%;
                background: linear-gradient(90deg, rgba(0, 0, 0, 0.836) 0%, rgba(0, 0, 0, 0.596) 35%, rgba(0, 212, 255, 0) 100%);
                top: 0;
                bottom: 0;
                z-index: -1;
            }

            .hero-bg-video {
                position: absolute;
                top: 0;
                right: 0;
                left: 0;
                bottom: 0;
                width: 100%;
                height: 100%;
                z-index: -1;

                video {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }
        }

    }

    &.three {
        background-image: url(../images/banner/09.webp);
    }
}

.banner-seven-inner-content {
    padding-left: 90px;
    position: relative;

    @media #{$large-mobile} {
        padding-left: 45px;
    }

    @media #{$small-mobile} {
        padding-left: 10px;
    }

    .counter-area-banner-7 {
        position: absolute;
        left: 90px;
        top: 50%;
        background: linear-gradient(90deg, #49565E 0%, rgba(95, 95, 95, 0) 100%);
        padding: 64px 57px;
        border-left: 8px solid rgba(255, 255, 255, 0.507);

        @media #{$mdsm-layout} {
            display: none;
        }

        .title-counter {
            font-size: 54px;
            color: #fff;
        }

        p {
            margin-bottom: 20px;
            text-transform: uppercase;
            color: #fff;
        }

        .stars-area-main {
            display: flex;
            align-items: center;
            gap: 5px;

            i {
                color: #fff;
            }
        }
    }

    .pre-title {
        display: flex;
        align-items: center;
        font-size: 20px;
        color: #FFFFFF;

        @media #{$large-mobile} {
            font-size: 16px;
            margin-bottom: 15px;
        }

        img {
            margin-right: 10px;
        }
    }

    .title {
        font-size: 160px;
        text-transform: uppercase;
        color: #fff;
        font-weight: 800;
        line-height: 1.1;

        @media #{$smlg-device} {
            font-size: 96px;
        }

        @media #{$mdsm-layout} {
            font-size: 86px;

            br {
                display: none;
            }
        }

        @media #{$sm-layout} {
            font-size: 54px !important;
        }

        @media #{$large-mobile} {
            font-size: 44px !important;
        }

        span {
            padding-left: 350px;

            @media #{$smlg-device} {
                padding-left: 0;
            }

            @media#{$small-mobile} {
                font-size: 46px;
            }

        }
    }

    p.disc {
        padding-left: 360px;
        max-width: 80%;
        color: #EAEAEA;
        font-size: 20px;
        line-height: 1.5;
        font-weight: 400;
        margin-bottom: 40px;

        @media #{$mdsm-layout} {
            padding-left: 0;
            max-width: 100%;
            margin-bottom: 40px;
        }
    }

    .rts-btn {
        margin-left: 360px;

        @media #{$mdsm-layout} {
            margin-left: 0;
        }
    }
}



.banner-seven-swiper-wrapper {
    position: relative;

    .language-area {
        writing-mode: vertical-rl;
        transform: rotate(180deg) translateY(-50%);
        position: absolute;
        top: 50%;
        left: 60px;
        display: flex;
        align-items: center;
        gap: 25px;
        z-index: 10;

        @media #{$large-mobile} {
            left: 25px;
        }

        @media #{$small-mobile} {
            display: none;
        }

        span {
            color: #fff;
            font-size: 16px;
        }
    }

    .to-bottom-area {
        writing-mode: vertical-rl;
        transform: rotate(180deg) translateY(-50%);
        position: absolute;
        bottom: 150px;
        left: 60px;
        display: flex;
        align-items: center;
        gap: 25px;
        color: #fff;
        z-index: 10;

        @media #{$large-mobile} {
            left: 25px;
        }

        @media #{$small-mobile} {
            display: none;
        }

        i {
            animation: jump-3 2s linear infinite;
        }
    }

    .email-area-left {
        writing-mode: vertical-rl;
        transform: rotate(180deg);
        position: absolute;
        top: 127px;
        left: 60px;
        display: flex;
        align-items: center;
        gap: 60px;
        z-index: 10;

        @media #{$large-mobile} {
            left: 25px;
        }

        @media #{$small-mobile} {
            display: none;
        }

        &::after {
            position: absolute;
            content: "";
            left: 12px;
            bottom: 148px;
            width: 1px;
            height: 40px;
            background: #fff;
        }

        a {
            font-weight: 600;
            font-size: 16px;
        }

        i {
            transform: rotate(90deg);
        }

        * {
            color: #fff;
        }
    }

    .swiper-pagination {
        max-width: max-content;
        display: flex;
        flex-direction: column;
        top: 50%;
        right: 120px;
        left: auto;
        transform: translateY(-50%);

        @media #{$mdsm-layout} {
            display: none;
        }

        .swiper-pagination-bullet {
            height: 60px;
            width: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: transparent;
            color: #fff;
        }

        .swiper-pagination-bullet-active {
            background-image: none;
            background: rgba(0, 0, 0, 0.2);
        }
    }
}


.banner-bg_eight {
    background-image: url(../images/banner/10.webp);
    height: 900px;
    display: flex;
    align-items: center;
    position: relative;

    @media #{$large-mobile} {
        height: 700px;
    }
}

.banner-inner-content-eight {
    padding-bottom: 160px;

    .pre-title-area {
        display: flex;
        align-items: center;
        gap: 15px;
        border: 1px solid #999999;
        padding: 5px 15px;
        border-radius: 10px;
        max-width: max-content;
        margin-bottom: 45px;

        p {
            margin-bottom: 0;
            font-size: 16px;
            color: #5D666F;
        }
    }

    .title {
        font-size: 100px;
        text-transform: uppercase;

        @media #{$laptop-device} {
            font-size: 91px;
        }

        @media #{$smlg-device} {
            font-size: 44px;
        }

        @media #{$large-mobile} {
            font-size: 32px;
        }

        span {
            font-weight: 400;
        }
    }

    p.disc {
        font-size: 18px;
        line-height: 1.5;
        color: #5D666F;
        max-width: 56%;

        @media #{$sm-layout} {
            max-width: 100%;
        }
    }
}

.uppercase {
    text-transform: uppercase;
}

.padding-extend-300 {
    padding-top: 300px !important;
}

.rts-banner-area-9 {
    background: #F2F2F2;
    height: 850px;
    display: flex;
    align-items: center;
    position: relative;
    z-index: 1;

    @media #{$smlg-device} {
        height: 630px;
    }

    @media #{$large-mobile} {
        height: 710px;
    }

    @media #{$small-mobile} {
        height: 500px;
    }

    span.pre {
        text-transform: uppercase;
        color: #1C2539;
        display: block;
        margin-bottom: 12px;
    }

    p.disc {
        font-size: 18px;
        color: #5D666F;
        line-height: 1.7;
    }

    .image-right-absolute {
        position: absolute;
        right: 0;
        top: 0;
        bottom: 0;
        clip-path: polygon(68.225% 100%, 0% 100%, 0% -0.411%, 43.944% 0%, 99.93% 0%, 99.93% 100%, 59.225% 100%);

        z-index: -1;

        @media #{$extra-device} {
            max-width: 700px;
            height: 100%;
            min-height: 100%;
            object-fit: cover;
        }

        @media #{$laptop-device} {
            max-width: 600px;
            height: 100%;
            min-height: 100%;
            object-fit: cover;
        }

        @media #{$smlg-device} {
            display: none;
        }

        img {
            @media #{$extra-device} {
                height: 100%;
                min-height: 100%;
            }

            @media #{$laptop-device} {
                height: 100%;
                min-height: 100%;
                object-fit: cover;
            }
        }
    }

    .circle-text-main {
        @media #{$large-mobile} {
            position: absolute;
            left: 74%;
            bottom: 15px;
            transform: translateX(-50%);
        }
    }
}


.easy-contact-left-funfacts-8 {
    background: #20282D;
    padding: 89px 66px;
    border-radius: 10px;
    margin-right: -40px;
    position: relative;
    z-index: 5;

    @media #{$mdsm-layout} {
        margin-right: 0;
        margin-top: 30px;
    }

    @media #{$sm-layout} {
        padding: 25px;
    }

    .signle-contact {
        display: flex;
        align-items: flex-start;
        gap: 30px;
        margin-bottom: 35px;

        @media #{$small-mobile} {
            flex-direction: column;
            gap: 15px;
        }

        &:last-child {
            margin-bottom: 0;
        }

        .icon {
            height: 51px;
            min-width: 51px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #F2F2F2;

            i {
                color: #20282D;
            }
        }

        .main-contact {
            span {
                display: block;
                color: #B3B7C1;
                font-size: 20px;
                margin-bottom: 10px;
            }

            a {
                font-size: 22px;
                color: #fff;
            }
        }
    }
}

.rts-fun-facts-area-8 {
    .modern-funfacts-area-wrapper {
        margin-left: -30px;
        padding-right: 20px;

        @media #{$mdsm-layout} {
            margin-left: 0;
            padding-right: 0;
            margin-top: 30px;
        }
    }

    .single-counter-wrapper-8 {
        padding: 35px 45px;
        background: #F2F2F2;
        margin-bottom: 50px;
        border-radius: 10px;
        text-align: center;

        .title {
            font-size: 70px;
            margin-bottom: 0;

            @media #{$laptop-device} {
                font-size: 40px;
            }

            @media #{$smlg-device} {
                font-size: 34px !important;
            }

            @media #{$sm-layout} {
                font-size: 38px !important;
            }
        }

        p {
            margin-bottom: 0;
            text-transform: uppercase;
            font-size: 16px;
            color: #1C2539;
            font-weight: 700;
        }

        &:last-child {
            margin-bottom: 0;
        }
    }
}

.counter-modern-lfun-facts-wrapper {
    margin-left: -50px;

    @media #{$mdsm-layout} {
        margin-left: 0;
        margin-top: 30px;
    }
}

.banner-bg_12 {
    height: 900px;
    background-image: url(../images/banner/13.webp);
    display: flex;
    align-items: center;

    @media #{$mdsm-layout} {
        height: 650px;
    }

    @media #{$large-mobile} {
        background-position: 75% center;
    }
}

.banner-inner-content-12 {
    .pre {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 20px;
        color: #FFFFFF;
    }

    .title {
        font-size: 80px;
        line-height: 1.1;
        color: #fff;

        @media #{$mdsm-layout} {
            font-size: 44px;
        }

        @media #{$large-mobile} {
            font-size: 36px;
        }
    }

    p.disc {
        font-size: 18px;
        color: #fff;
        max-width: 55%;
        font-weight: 300;
        line-height: 1.8;
        margin-bottom: 53px;

        @media #{$mdsm-layout} {
            max-width: 75%;
        }

        @media #{$large-mobile} {
            max-width: 100%;
        }
    }
}

.flex-end-footer {
    justify-content: flex-end;

    @media #{$mdsm-layout} {
        justify-content: flex-start;
        margin-top: 25px;
    }
}

.rts-banner-ten-area {
    position: relative;
    z-index: 1;

    @media #{$large-mobile} {
        &::after {
            position: absolute;
            content: '';
            left: 0;
            bottom: 0;
            width: 100%;
            height: 100%;
            background: #ffffff63;
            z-index: -1;
        }
    }

    .chart-image {
        img {
            animation: jump-2 5s linear infinite;
        }

        .one {
            position: absolute;
            right: 317px;
            top: 40%;
            transform: translateY(-50%);

            @media #{$smlg-device} {
                right: 50px;
            }

            @media #{$mdsm-layout} {
                display: none;
            }
        }

        .two {
            position: absolute;
            left: 52%;
            bottom: 99px;
            animation: jump-2 10s linear infinite;

            @media #{$mdsm-layout} {
                max-width: 133px;
                left: 75%;
            }

            @media #{$mdsm-layout} {
                display: none;
            }
        }
    }

}

.rts-banner-five-wrapper {
    position: relative;
    z-index: 1;

    .shape-area {
        img {
            z-index: -1;
        }

        .one {
            position: absolute;
            left: 50%;
            top: 220px;
        }

        .two {
            position: absolute;
            right: 80px;
            top: 40%;
        }

        .three {
            position: absolute;
            left: 140px;
            top: 55%;
            z-index: -1;
        }
    }
}

.blog-details-banner-large-image {
    height: 703px;
    background-image: url(../images/blog/18.webp);
    background-attachment: fixed;
    background-position: center, center;
    background-size: cover;

    @media #{$sm-layout} {
        height: 450px;
    }

    @media #{$large-mobile} {
        height: 350px;
    }
}

.partner-breadcrumb {
    background-image: url(../images/banner/23.webp);
    height: 477px;

    @media #{$large-mobile} {
        height: 400px;
    }
}


.our-partner-bottom-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 65px;

    @media #{$mdsm-layout} {
        flex-wrap: wrap;
        gap: 20px;
    }

    &>* {
        flex-basis: 50%;

        @media #{$mdsm-layout} {
            flex-basis: 100%;
        }
    }

    .title {
        font-size: 42px;

        @media #{$mdsm-layout} {
            font-size: 32px;
        }

        @media #{$large-mobile} {
            font-size: 25px;
        }
    }
}


.with-video {
    .banner-seven-inner-content .title span {
        padding-left: 0;
    }

    .banner-seven-inner-content p.disc {
        padding-left: 0;
    }

    .banner-seven-inner-content .rts-btn {
        margin-left: 0;
    }

    .banner-seven-inner-content {
        padding-left: 0;

        @media #{$smlg-device} {
            padding-left: 70px;
        }

        @media #{$md-layout} {
            padding-left: 70px;
        }

        @media #{$small-mobile} {
            padding-left: 0;
        }
    }
}

.banner-accountent-bg {
    background-image: url(../images/banner/28.webp);
    height: 900px;
    display: flex;
    align-items: center;

    @media #{$mdsm-layout} {
        height: auto;
    }
}

.banner-accountent-content {
    .pre-title {
        display: flex;
        align-content: center;
        gap: 10px;
        font-size: 20px;
        color: #1C2539;
        margin-bottom: 10px;
    }

    .title {
        font-size: 80px;

        @media #{$sm-layout} {
            font-size: 50px;
        }

        @media #{$large-mobile} {
            font-size: 44px;
        }

        @media #{$small-mobile} {
            font-size: 32px;
        }
    }

    p.disc {
        font-size: 18px;
    }

    .button-wrapper {
        .btn-border {
            border-color: var(--color-primary);
            color: #1C2539;

            &::before {
                background: var(--color-primary);
            }

            &:hover {
                color: #fff;
            }
        }
    }
}

.thumbnail-banner-area-accountent {
    position: relative;

    .absolute-rating-area {
        position: absolute;
        left: -1%;
        padding: 20px 27px;
        background: #FFFFFF;
        border-radius: 20px;
        top: 11%;
        text-align: center;
        display: flex;
        flex-direction: column;
        justify-content: center;

        img {
            margin-bottom: 15px;
        }

        .stars-area {
            display: flex;
            align-items: center;
            gap: 7px;
            margin-bottom: 14px;

            i {
                color: #FBB002;
            }
        }

        span {
            color: #5D666F;
            font-size: 22px;
            font-weight: 500;
        }
    }
}


.rts-banner-accountent-two {
    background-image: url(../images/banner/29.webp);
    height: 1020px;
    display: flex;
    align-items: center;

    @media #{$mdsm-layout} {
        height: 700px;
    }

    .banner-accountent-content {
        padding: 45px 51px;
        background: rgba(32, 40, 45, 0.34);
        border-radius: 7px 30px 30px 7px;
        border-left: 7px solid var(--color-primary);

        @media #{$large-mobile} {
            padding: 40px 20px;
        }

        .pre-title,
        .title,
        p.disc {
            color: #fff;
        }

        .pre-title {
            img {
                filter: brightness(0) saturate(100%) invert(100%) sepia(12%) saturate(7488%) hue-rotate(268deg) brightness(125%) contrast(99%);
            }
        }

        .btn-border {
            border-color: #fff;
            color: #fff;

            &:hover {
                border-color: var(--color-primary);
            }
        }
    }
}


.rts-banner-hr {
    clip-path: polygon(0% 92.405%, 0% 0%, 100% 0%, 100% 92.405%, 100% 92.405%, 98.982% 94.447%, 97.636% 96.315%, 95.99% 97.904%, 94.075% 99.109%, 91.919% 99.825%, 89.553% 99.948%, 87.005% 99.372%, 84.305% 97.993%, 81.483% 95.706%, 78.568% 92.405%, 78.568% 92.405%, 75.926% 89.487%, 73.234% 87.448%, 70.533% 86.201%, 67.866% 85.661%, 65.277% 85.74%, 62.806% 86.352%, 60.498% 87.411%, 58.395% 88.83%, 56.54% 90.524%, 54.974% 92.405%, 54.974% 92.405%, 53.091% 94.564%, 51.053% 96.359%, 48.84% 97.748%, 46.432% 98.686%, 43.809% 99.13%, 40.949% 99.035%, 37.833% 98.359%, 34.441% 97.058%, 30.751% 95.088%, 26.745% 92.405%, 26.745% 92.405%, 22.475% 89.584%, 18.583% 87.6%, 15.059% 86.373%, 11.895% 85.825%, 9.083% 85.878%, 6.613% 86.453%, 4.479% 87.472%, 2.671% 88.856%, 1.181% 90.526%, 0% 92.405%);
    height: 947.81px;
    background-image: url(../images/banner/30.webp);
    @media #{$sm-layout} {
        height: 600px;
    }
    @media #{$large-mobile} {
        height: 500px;
    }
}

.rts-banner-hr {
    display: flex;
    align-items: center;
    .banner-hr-content-wrapper {
        text-align: center;
        max-width: 614px;
        margin: auto;
        position: relative;
        z-index: 1;

        &::after {
            content: '';
            border-radius: 50%;
            position: absolute;
            width: 506px;
            height: 506px;
            left: 50%;
            top: 40%;
            transform: translate(-50%, -50%);
            background: #493BFF;
            opacity: 0.3;
            z-index: -1;
        }
        &::before {
            content: '';
            border-radius: 50%;
            position: absolute;
            width: 710px;
            height: 710px;
            left: 50%;
            top: 40%;
            transform: translate(-50%, -50%);
            background: #493BFF;
            opacity: 0.3;
            z-index: -1;
        }

        .pre-title,
        .title,
        .disc {
            color: #fff;
        }
        .pre-title{
            display: flex;
            align-items: flex-end;
            justify-content: center;
            gap: 10px;
            img{
                filter: brightness(0) saturate(100%) invert(100%) sepia(6%) saturate(7500%) hue-rotate(292deg) brightness(117%) contrast(105%);
            }
        }
        .rts-btn {
            margin: auto;
        }
    }
}