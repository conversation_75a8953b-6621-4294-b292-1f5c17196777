.single-working-process-one {
    display: flex;
    align-items: center;
    gap: 110px;
    margin-bottom: 40px;

    // @media #{$sm-layout} {
    //     gap: 25px;
    //     flex-direction: column;
    // }
    @media #{$large-mobile} {
        gap: 25px;
    }

    &:last-child {
        margin-bottom: 0;
    }

    .left {
        position: relative;
        z-index: 5;

        .icon {
            height: 110px;
            width: 110px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #3E484E;
            border-radius: 100%;
            position: relative;

            @media #{$large-mobile} {
                height: 90px;
                width: 90px;
            }

            &::after {
                position: absolute;
                content: "";
                background-image: url(../images/process/01.png);
                height: 19px;
                width: 19px;
                background-repeat: no-repeat;
                background-position: center;
                right: -8px;
                top: 50%;
                transform: translateY(-50%);

                @media #{$large-mobile} {
                    display: none;
                }
            }
        }
    }

    .inner-content {
        border-radius: 10px;
        padding: 40px 50px;
        background: #3E484E;
        position: relative;
        z-index: 1;

        @media #{$small-mobile} {
            padding: 30px 5px 30px 50px;
        }

        .number-main {
            position: absolute;
            top: 50%;
            left: 0;
            transform: translateY(-50%);

            &::after {
                content: '';
                position: absolute;
                height: 50px;
                width: 50px;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
                background: #3E484E;
                z-index: -2;
                border-radius: 50%;

                @media #{$large-mobile} {
                    display: none;
                }
            }

            &::before {
                content: '';
                position: absolute;
                width: 150px;
                border: 1px dashed #474E52;
                left: -150px;
                z-index: -2;

                @media #{$small-mobile} {
                    display: none;
                }
            }
        }

        .number {
            color: #fff;
            width: 32px;
            height: 32px;
            border-radius: 500%;
            background: #20282D;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 600;
            left: -16px;
            z-index: -1;
            position: absolute;
            top: 50%;
            transform: translateY(-16px);

            @media #{$large-mobile} {
                left: 10px;
            }
        }

        .title {
            color: #fff;
            font-size: 20px;
            margin-bottom: 15px;
        }

        p.disc {
            color: #C9C9C9;
        }
    }
}

.thumbnail-working-procss-one {
    img {
        border-radius: 50%;
        overflow: hidden;
        margin-left: 50px;

        @media #{$mdsm-layout} {
            margin-left: 0;
        }

        @media #{$sm-layout} {
            margin-left: 0;
            border-radius: 5px;
        }
    }
}

.working-process-one.without-clip-radious {

    .thumbnail-working-procss-one img {
        border-radius: 10px;
    }

    .single-working-process-one .inner-content {
        border-radius: 0;
    }

    .single-working-process-one .left .icon {
        border-radius: 0;
    }
}

.business-process-hr-left-thumbnail {
    display: flex;
    justify-content: center;
    max-width: 456px;
    margin: auto;
    position: relative;

    .right-top {
        display: flex;
        align-items: center;
        gap: 25px;
        padding: 20px 35px;
        border-radius: 20px;
        border: 1px solid #E3E0E6;
        position: absolute;
        top: 40px;
        right: -60px;
        background: #fff;

        @media #{$smlg-device} {
            right: 15px;
        }

        .title {
            margin-bottom: 0px;
            font-size: 36px;
        }

        &.bottom {
            top: auto;
            bottom: 40px;
            left: -60px;
            right: auto;

            @media #{$sm-layout} {
                left: 15px;
            }
        }
    }
}

.business-process-hr-content {
    .title {
        margin-top: 20px;
    }

    p.disc {
        margin-top: 20px;
        margin-bottom: 40px;
    }
}

.color-blue-demo {

    .title-area-client-client::before,
    .title-area-client-client::after {
        background: #1c2539;
    }
}