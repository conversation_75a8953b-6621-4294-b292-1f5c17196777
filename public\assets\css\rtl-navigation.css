/* RTL Navigation Enhancements for Arabic */

/* RTL Support for Navigation */
[dir="rtl"] .nav-menu {
    direction: rtl;
}

[dir="rtl"] .nav-link {
    text-align: right;
}

[dir="rtl"] .mega-column a:hover {
    transform: translateX(-10px);
}

[dir="rtl"] .mobile-menu {
    right: auto;
    left: -100%;
}

[dir="rtl"] .mobile-menu-overlay.active .mobile-menu {
    right: auto;
    left: 0;
}

[dir="rtl"] .mobile-nav-link::before {
    right: 0;
    left: auto;
}

[dir="rtl"] .mobile-nav-link:hover {
    padding-right: 2rem;
    padding-left: 1.5rem;
}

[dir="rtl"] .mobile-submenu a::before {
    content: '←';
    right: 2rem;
    left: auto;
}

[dir="rtl"] .mobile-submenu a:hover::before {
    right: 2.5rem;
}

[dir="rtl"] .mobile-submenu a:hover {
    padding-right: 3.5rem;
    padding-left: 3rem;
}

/* Arabic Typography Enhancements */
.nav-link,
.mobile-nav-link,
.mega-column h4,
.featured-product h5 {
    font-family: 'Cairo', '<PERSON>jawal', '<PERSON><PERSON>', sans-serif;
    font-weight: 600;
}

.mega-column a,
.mobile-submenu a,
.contact-info p {
    font-family: 'Cairo', 'Tajawal', sans-serif;
    font-weight: 400;
}

/* Enhanced Arabic Menu Styling */
.nav-link {
    font-size: 1rem;
    letter-spacing: 0.5px;
}

.mega-column h4 {
    font-size: 1.1rem;
    font-weight: 700;
    color: #2c3e50;
}

.mega-column a {
    font-size: 0.95rem;
    line-height: 1.6;
}

.mobile-nav-link {
    font-size: 1.05rem;
    font-weight: 600;
}

.mobile-submenu a {
    font-size: 0.9rem;
    font-weight: 500;
}

/* Arabic-specific hover effects */
.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #007bff, #0056b3);
    transition: all 0.3s ease;
    transform: translateX(50%);
}

[dir="rtl"] .nav-link::after {
    right: 50%;
    left: auto;
    transform: translateX(50%);
}

.nav-link:hover::after {
    width: 100%;
}

/* Enhanced mega menu for Arabic */
.mega-column h4 {
    position: relative;
    padding-bottom: 1rem;
    margin-bottom: 1.5rem;
}

.mega-column h4::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #007bff, #0056b3);
    border-radius: 2px;
}

[dir="rtl"] .mega-column h4::before {
    right: 0;
    left: auto;
}

.mega-column h4::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 60px;
    height: 2px;
    background: #FFD700;
    border-radius: 1px;
}

[dir="rtl"] .mega-column h4::after {
    right: 0;
    left: auto;
}

/* Mobile menu Arabic enhancements */
.mobile-menu-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid #007bff;
}

.mobile-nav-item {
    position: relative;
}

.mobile-nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0,123,255,0.1));
    transition: width 0.3s ease;
}

[dir="rtl"] .mobile-nav-item::before {
    right: 0;
    left: auto;
}

.mobile-nav-item:hover::before {
    width: 100%;
}

/* Arabic social links styling */
.social-links {
    justify-content: center;
    gap: 1.5rem;
}

.social-link {
    position: relative;
    overflow: hidden;
}

.social-link::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255,255,255,0.3);
    border-radius: 50%;
    transition: all 0.3s ease;
    transform: translate(-50%, -50%);
}

.social-link:hover::before {
    width: 100%;
    height: 100%;
}

/* Contact info Arabic styling */
.contact-info p {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.contact-info p:hover {
    background: rgba(0,123,255,0.1);
    transform: translateX(-5px);
}

[dir="rtl"] .contact-info p:hover {
    transform: translateX(5px);
}

/* Featured product Arabic styling */
.featured-product {
    text-align: center;
}

.featured-product h5 {
    font-size: 1.2rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 1rem 0 0.5rem;
}

.featured-product p {
    font-size: 0.9rem;
    color: #666;
    line-height: 1.6;
    margin-bottom: 1rem;
}

/* Button enhancements for Arabic */
.btn-small {
    font-family: 'Cairo', 'Tajawal', sans-serif;
    font-weight: 600;
    font-size: 0.9rem;
    padding: 0.75rem 1.5rem;
    position: relative;
    overflow: hidden;
}

.btn-small::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.btn-small:hover::before {
    left: 100%;
}

/* Submenu toggle Arabic styling */
.submenu-toggle {
    font-family: 'Cairo', sans-serif;
    font-weight: 700;
    font-size: 1.1rem;
}

/* Animation improvements for Arabic */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

[dir="rtl"] .mobile-nav-item {
    animation: slideInLeft 0.3s ease forwards;
}

/* Responsive Arabic adjustments */
@media (max-width: 768px) {
    .nav-link {
        font-size: 0.95rem;
    }
    
    .mega-column h4 {
        font-size: 1rem;
    }
    
    .mobile-nav-link {
        font-size: 1rem;
    }
    
    .mobile-menu {
        width: 100%;
        max-width: 100vw;
    }
    
    .contact-info p {
        font-size: 0.85rem;
    }
}

@media (max-width: 480px) {
    .mobile-menu-header {
        padding: 1rem;
    }
    
    .mobile-nav-link {
        padding: 0.75rem 1rem;
        font-size: 0.95rem;
    }
    
    .mobile-submenu a {
        padding: 0.5rem 2rem;
        font-size: 0.85rem;
    }
    
    .social-links {
        gap: 1rem;
    }
    
    .social-link {
        width: 40px;
        height: 40px;
        line-height: 40px;
        font-size: 1rem;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .nav-link,
    .mobile-nav-link {
        color: #000;
        font-weight: 700;
    }
    
    .mega-column a,
    .mobile-submenu a {
        color: #333;
        font-weight: 600;
    }
    
    .mobile-menu {
        border: 2px solid #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .nav-link,
    .mobile-nav-link,
    .mega-column a,
    .mobile-submenu a,
    .btn-small,
    .social-link {
        transition: none;
    }
    
    .mobile-nav-item {
        animation: none;
        opacity: 1;
        transform: none;
    }
}
