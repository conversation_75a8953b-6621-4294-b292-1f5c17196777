.contact-info-area-wrapper-p {
    background: var(--color-primary);
    padding: 90px 60px;
    border-radius: 10px;
    margin-right: -30px;
    z-index: 2;
    position: relative;

    @media #{$mdsm-layout} {
        margin-right: 0;
        padding: 25px;
    }

    .single-contact-info {
        display: flex;
        align-items: center;
        gap: 20px;
        margin-bottom: 60px;

        @media #{$large-mobile} {
            flex-direction: column;
            align-items: flex-start;
            margin-bottom: 10px;
        }

        &:last-child {
            margin-bottom: 0;
        }

        .icon {
            height: 51px;
            width: 51px;
            background: #fff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;

            i {
                color: var(--color-primary);
            }
        }

        .info-wrapper {
            span {
                color: #B3B7C1;
                display: block;
                margin-bottom: 10px;
            }

            a {
                color: #FFFFFF;
                font-size: 22px;
                font-weight: 500;
            }
        }
    }

    &.new {
        margin-right: 0;
        padding: 93px 60px;

        @media #{$large-mobile} {
            padding: 35px;
            margin-bottom: 25px;
        }
    }
}

.rts-contact-area-in-page {
    margin-top: -120px;
}

body.contact-page {
    .rts-breadcrumb-area {
        .title-area-left {
            margin-top: -70px;
        }
    }
}

#form-messages {
    margin-bottom: 20px;
}

#form-messages.success {
    color: green;
}

.contact-form-p {
    margin-left: -90px;
    background: #fff;
    border-radius: 10px;
    padding: 40px;
    border: 1px solid rgba(32, 40, 45, 0.2);

    @media #{$mdsm-layout} {
        margin-left: 0;
        padding: 25px;
    }

    input {
        height: 54px;
        border: 1px solid rgba(32, 40, 45, 0.2);
        border-radius: 6px;
        margin-bottom: 30px;
    }

    textarea {
        margin-bottom: 30px;
        border: 1px solid rgba(32, 40, 45, 0.2);
        border-radius: 6px;
        height: 92px;
        padding: 10px 15px;

        &:focus {
            border: 1px solid var(--color-primary);
        }
    }

    &.new {
        margin-left: 0;
    }
}

.thumbnail-contact-form {
    margin: 0 -30px;

    @media #{$sm-layout} {
        margin: 0 0;
    }
}


.contactform-accountentarea {
    background-image: url(../images/appoinment/07.webp);

    .appoinment-left {
        height: 100%;

        .easy-contact-left-funfacts-8 {
            height: 100%;

            .pre {
                text-transform: uppercase;
                font-size: 16px;
                color: #FFFFFF;
            }

            .title {
                font-size: 48px;
                color: #fff;
                margin-bottom: 55px;
                margin-top: 12px;

                @media #{$md-layout} {
                    font-style: 30px;
                }

                @media #{$sm-layout} {
                    font-size: 28px;
                }
            }

            .signle-contact {
                margin-bottom: 56px;

                .icon {
                    background: #3E484E;
                    height: 59.81px;
                    width: 59.81px;
                    border-radius: 8px;

                    i {
                        color: #FFFFFF;
                    }
                }

                .main-contact {
                    span {
                        color: #fff;
                        font-size: 24px;
                    }

                    a {
                        color: #8B8F99;
                        font-size: 22px;
                    }
                }
            }
        }
    }
}

#form-messages.error {
    color: red;
}

#form-messages.success {
    color: green;
}