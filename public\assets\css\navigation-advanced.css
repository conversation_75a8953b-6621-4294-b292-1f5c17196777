/* Advanced Navigation Styles */

/* Enhanced Mega Menu */
.nav-menu {
    display: flex;
    align-items: center;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-link {
    position: relative;
    color: #333;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 1rem 1.5rem;
    display: block;
}

.nav-link:hover {
    color: #007bff;
    transform: translateY(-2px);
}

/* Mega Menu Enhanced Styles */
.rts-mega-menu {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background: white;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-20px);
    transition: all 0.4s ease;
    z-index: 1000;
    border-radius: 0 0 15px 15px;
    overflow: hidden;
}

.main-nav:hover .rts-mega-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.mega-menu-content {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.mega-column h4 {
    color: #333;
    margin-bottom: 1rem;
    font-weight: 600;
    border-bottom: 2px solid #007bff;
    padding-bottom: 0.5rem;
    position: relative;
}

.mega-column h4::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 30px;
    height: 2px;
    background: #FFD700;
}

.mega-column ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.mega-column li {
    margin-bottom: 0.5rem;
}

.mega-column a {
    color: #666;
    text-decoration: none;
    transition: all 0.3s ease;
    display: block;
    padding: 0.5rem 0;
    border-radius: 5px;
    position: relative;
    overflow: hidden;
}

.mega-column a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0,123,255,0.1), transparent);
    transition: left 0.5s ease;
}

.mega-column a:hover::before {
    left: 100%;
}

.mega-column a:hover {
    color: #007bff;
    transform: translateX(10px);
    background: rgba(0,123,255,0.05);
    padding-left: 1rem;
}

.mega-featured {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.mega-featured::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(0,123,255,0.1), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.featured-product {
    position: relative;
    z-index: 2;
}

.featured-product img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 10px;
    margin-bottom: 1rem;
    transition: transform 0.3s ease;
}

.featured-product:hover img {
    transform: scale(1.05);
}

.btn-small {
    display: inline-block;
    padding: 0.5rem 1rem;
    background: #007bff;
    color: white;
    text-decoration: none;
    border-radius: 25px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    margin-top: 1rem;
}

.btn-small:hover {
    background: #0056b3;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,123,255,0.3);
}

/* Mobile Menu Styles */
.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.mobile-menu-overlay.active {
    opacity: 1;
    visibility: visible;
}

.mobile-menu {
    position: absolute;
    top: 0;
    right: -100%;
    width: 350px;
    max-width: 90vw;
    height: 100%;
    background: white;
    transition: right 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    overflow-y: auto;
    box-shadow: -10px 0 30px rgba(0,0,0,0.3);
}

.mobile-menu-overlay.active .mobile-menu {
    right: 0;
}

.mobile-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #eee;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.close-mobile-menu {
    background: #ff4757;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-mobile-menu:hover {
    background: #ff3742;
    transform: rotate(90deg);
}

.mobile-nav-item {
    border-bottom: 1px solid #f0f0f0;
}

.mobile-nav-link {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

.mobile-nav-link::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(90deg, #007bff, #0056b3);
    transition: width 0.3s ease;
    z-index: -1;
}

.mobile-nav-link:hover::before {
    width: 5px;
}

.mobile-nav-link:hover {
    background: rgba(0,123,255,0.05);
    color: #007bff;
    padding-left: 2rem;
}

.submenu-toggle {
    background: #007bff;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.submenu-toggle:hover {
    background: #0056b3;
    transform: scale(1.1);
}

.mobile-submenu {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s ease;
    background: #f8f9fa;
}

.mobile-submenu.active {
    max-height: 400px;
}

.mobile-submenu a {
    display: block;
    padding: 0.75rem 3rem;
    color: #666;
    text-decoration: none;
    border-bottom: 1px solid #e9ecef;
    transition: all 0.3s ease;
    position: relative;
}

.mobile-submenu a::before {
    content: '→';
    position: absolute;
    left: 2rem;
    opacity: 0;
    transition: all 0.3s ease;
}

.mobile-submenu a:hover::before {
    opacity: 1;
    left: 2.5rem;
}

.mobile-submenu a:hover {
    background: white;
    color: #007bff;
    padding-left: 3.5rem;
}

.mobile-menu-footer {
    padding: 2rem 1.5rem;
    border-top: 1px solid #eee;
    margin-top: auto;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.contact-info p {
    margin: 0.75rem 0;
    color: #666;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
    justify-content: center;
}

.social-link {
    display: inline-block;
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    text-align: center;
    line-height: 45px;
    border-radius: 50%;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 1.2rem;
}

.social-link:hover {
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 5px 15px rgba(0,123,255,0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
    .mobile-menu {
        width: 100%;
        right: -100%;
    }
    
    .mega-menu-content {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 1rem;
    }
}

/* Animation for menu items */
.mobile-nav-item {
    opacity: 0;
    transform: translateX(50px);
    animation: slideInRight 0.3s ease forwards;
}

.mobile-nav-item:nth-child(1) { animation-delay: 0.1s; }
.mobile-nav-item:nth-child(2) { animation-delay: 0.2s; }
.mobile-nav-item:nth-child(3) { animation-delay: 0.3s; }
.mobile-nav-item:nth-child(4) { animation-delay: 0.4s; }
.mobile-nav-item:nth-child(5) { animation-delay: 0.5s; }
.mobile-nav-item:nth-child(6) { animation-delay: 0.6s; }

@keyframes slideInRight {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}
