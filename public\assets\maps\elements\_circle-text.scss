.circle-text-main {
    position: absolute;
    left: 52%;
    bottom: 75px;
    transform: translateX(-50%);

    .circle {
        position: relative;
        width: 170px;
        height: 170px;
        border-radius: 100vmax;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #fff;
        padding: 10px;
    }

    .logo {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
    }

    .text {
        position: absolute;
        width: 100%;
        height: 100%;
        font-family: consolas;
        color: #1C2539;
        text-transform: uppercase;
        font-size: 17px;
        animation: textRotation 8s linear infinite;
    }

    .text-1 {
        position: absolute;
        width: 100%;
        height: 100%;
        font-family: consolas;
        color: #1C2539;
        text-transform: uppercase;
        font-size: 17px;
        animation: textRotation 8s linear infinite;
    }

    @keyframes textRotation {
        to {
            transform: rotate(360deg);
        }
    }

    .text span {
        position: absolute;
        left: 50%;
        transform-origin: 0 84px;
    }

    .text-1 span {
        position: absolute;
        left: 50%;
        transform-origin: 0 84px;
    }

    .vedio-icone {
        opacity: 0;

        .video-play-button {
            height: 100%;
            width: 100%;
            padding: 0;
        }
    }
}

.testimonials-full-screen-area-wrapper {
    .circle-text-main {
        position: relative;
    }
}