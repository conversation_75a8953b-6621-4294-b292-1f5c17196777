/**
 * Advanced Navigation System
 * Enhanced mobile menu and mega menu interactions
 */

class AdvancedNavigation {
    constructor() {
        this.mobileMenuOverlay = document.querySelector('.mobile-menu-overlay');
        this.mobileMenu = document.querySelector('.mobile-menu');
        this.mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
        this.closeMobileMenu = document.querySelector('.close-mobile-menu');
        this.submenuToggles = document.querySelectorAll('.submenu-toggle');
        this.mobileSubmenuItems = document.querySelectorAll('.mobile-submenu');
        
        this.init();
    }
    
    init() {
        this.setupMobileMenu();
        this.setupSubmenuToggles();
        this.setupSmoothScrolling();
        this.setupMegaMenuEnhancements();
        this.setupKeyboardNavigation();
        this.setupClickOutside();
    }
    
    setupMobileMenu() {
        // Open mobile menu
        if (this.mobileMenuToggle) {
            this.mobileMenuToggle.addEventListener('click', (e) => {
                e.preventDefault();
                this.openMobileMenu();
            });
        }
        
        // Close mobile menu
        if (this.closeMobileMenu) {
            this.closeMobileMenu.addEventListener('click', (e) => {
                e.preventDefault();
                this.closeMobileMenuHandler();
            });
        }
        
        // Close on overlay click
        if (this.mobileMenuOverlay) {
            this.mobileMenuOverlay.addEventListener('click', (e) => {
                if (e.target === this.mobileMenuOverlay) {
                    this.closeMobileMenuHandler();
                }
            });
        }
        
        // Close on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.mobileMenuOverlay?.classList.contains('active')) {
                this.closeMobileMenuHandler();
            }
        });
    }
    
    openMobileMenu() {
        if (this.mobileMenuOverlay) {
            this.mobileMenuOverlay.classList.add('active');
            document.body.style.overflow = 'hidden';
            
            // Animate menu items
            this.animateMenuItems();
            
            // Focus management
            setTimeout(() => {
                this.closeMobileMenu?.focus();
            }, 300);
        }
    }
    
    closeMobileMenuHandler() {
        if (this.mobileMenuOverlay) {
            this.mobileMenuOverlay.classList.remove('active');
            document.body.style.overflow = '';
            
            // Close all submenus
            this.closeAllSubmenus();
            
            // Return focus to toggle button
            this.mobileMenuToggle?.focus();
        }
    }
    
    animateMenuItems() {
        const menuItems = document.querySelectorAll('.mobile-nav-item');
        menuItems.forEach((item, index) => {
            item.style.animationDelay = `${(index + 1) * 0.1}s`;
        });
    }
    
    setupSubmenuToggles() {
        this.submenuToggles.forEach(toggle => {
            toggle.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                
                const parentItem = toggle.closest('.mobile-nav-item');
                const submenu = parentItem.querySelector('.mobile-submenu');
                const isActive = submenu?.classList.contains('active');
                
                // Close all other submenus
                this.closeAllSubmenus();
                
                // Toggle current submenu
                if (!isActive && submenu) {
                    this.openSubmenu(submenu, toggle);
                }
            });
        });
    }
    
    openSubmenu(submenu, toggle) {
        submenu.classList.add('active');
        toggle.textContent = '−';
        toggle.style.transform = 'rotate(180deg)';
        
        // Animate submenu items
        const submenuItems = submenu.querySelectorAll('a');
        submenuItems.forEach((item, index) => {
            item.style.opacity = '0';
            item.style.transform = 'translateX(20px)';
            
            setTimeout(() => {
                item.style.transition = 'all 0.3s ease';
                item.style.opacity = '1';
                item.style.transform = 'translateX(0)';
            }, index * 50);
        });
    }
    
    closeAllSubmenus() {
        this.mobileSubmenuItems.forEach(submenu => {
            submenu.classList.remove('active');
        });
        
        this.submenuToggles.forEach(toggle => {
            toggle.textContent = '+';
            toggle.style.transform = 'rotate(0deg)';
        });
    }
    
    setupSmoothScrolling() {
        // Smooth scroll for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                const href = anchor.getAttribute('href');
                if (href === '#') return;
                
                e.preventDefault();
                const target = document.querySelector(href);
                
                if (target) {
                    // Close mobile menu if open
                    if (this.mobileMenuOverlay?.classList.contains('active')) {
                        this.closeMobileMenuHandler();
                    }
                    
                    // Smooth scroll to target
                    setTimeout(() => {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }, 300);
                }
            });
        });
    }
    
    setupMegaMenuEnhancements() {
        const megaMenuItems = document.querySelectorAll('.has-dropdown.mega-menu');
        
        megaMenuItems.forEach(item => {
            const megaMenu = item.querySelector('.rts-mega-menu');
            let hoverTimeout;
            
            if (megaMenu) {
                // Mouse enter
                item.addEventListener('mouseenter', () => {
                    clearTimeout(hoverTimeout);
                    this.showMegaMenu(megaMenu);
                });
                
                // Mouse leave
                item.addEventListener('mouseleave', () => {
                    hoverTimeout = setTimeout(() => {
                        this.hideMegaMenu(megaMenu);
                    }, 100);
                });
                
                // Keep menu open when hovering over it
                megaMenu.addEventListener('mouseenter', () => {
                    clearTimeout(hoverTimeout);
                });
                
                megaMenu.addEventListener('mouseleave', () => {
                    this.hideMegaMenu(megaMenu);
                });
            }
        });
    }
    
    showMegaMenu(megaMenu) {
        megaMenu.style.opacity = '1';
        megaMenu.style.visibility = 'visible';
        megaMenu.style.transform = 'translateY(0)';
        
        // Animate menu items
        const menuItems = megaMenu.querySelectorAll('.mega-menu-item li');
        menuItems.forEach((item, index) => {
            item.style.opacity = '0';
            item.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                item.style.transition = 'all 0.3s ease';
                item.style.opacity = '1';
                item.style.transform = 'translateY(0)';
            }, index * 30);
        });
    }
    
    hideMegaMenu(megaMenu) {
        megaMenu.style.opacity = '0';
        megaMenu.style.visibility = 'hidden';
        megaMenu.style.transform = 'translateY(-20px)';
    }
    
    setupKeyboardNavigation() {
        // Handle keyboard navigation for accessibility
        const navLinks = document.querySelectorAll('.nav-link, .mobile-nav-link');
        
        navLinks.forEach(link => {
            link.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    link.click();
                }
            });
        });
        
        // Tab navigation for mega menus
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                const activeElement = document.activeElement;
                const megaMenu = activeElement.closest('.rts-mega-menu');
                
                if (megaMenu && !megaMenu.contains(e.target)) {
                    this.hideMegaMenu(megaMenu);
                }
            }
        });
    }
    
    setupClickOutside() {
        document.addEventListener('click', (e) => {
            const megaMenus = document.querySelectorAll('.rts-mega-menu');
            
            megaMenus.forEach(menu => {
                const parentItem = menu.closest('.has-dropdown');
                
                if (!parentItem.contains(e.target)) {
                    this.hideMegaMenu(menu);
                }
            });
        });
    }
    
    // Public methods for external use
    openMobileMenuExternal() {
        this.openMobileMenu();
    }
    
    closeMobileMenuExternal() {
        this.closeMobileMenuHandler();
    }
    
    toggleMobileMenu() {
        if (this.mobileMenuOverlay?.classList.contains('active')) {
            this.closeMobileMenuHandler();
        } else {
            this.openMobileMenu();
        }
    }
}

// Enhanced Menu Interactions
class MenuEnhancements {
    constructor() {
        this.init();
    }
    
    init() {
        this.setupHoverEffects();
        this.setupLoadingStates();
        this.setupSearchFunctionality();
    }
    
    setupHoverEffects() {
        // Add ripple effect to buttons
        const buttons = document.querySelectorAll('.btn-small, .social-link, .submenu-toggle');
        
        buttons.forEach(button => {
            button.addEventListener('click', (e) => {
                this.createRipple(e, button);
            });
        });
    }
    
    createRipple(event, element) {
        const ripple = document.createElement('span');
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;
        
        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.classList.add('ripple');
        
        element.appendChild(ripple);
        
        setTimeout(() => {
            ripple.remove();
        }, 600);
    }
    
    setupLoadingStates() {
        // Add loading states for navigation links
        const navLinks = document.querySelectorAll('.nav-link[href]:not([href="#"])');
        
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                if (!link.href.startsWith('#')) {
                    this.showLoadingState(link);
                }
            });
        });
    }
    
    showLoadingState(element) {
        const originalText = element.textContent;
        element.style.opacity = '0.7';
        element.style.pointerEvents = 'none';
        
        // Reset after page load or timeout
        setTimeout(() => {
            element.style.opacity = '1';
            element.style.pointerEvents = 'auto';
        }, 2000);
    }
    
    setupSearchFunctionality() {
        // Add quick search functionality (if search input exists)
        const searchInput = document.querySelector('.nav-search-input');
        
        if (searchInput) {
            let searchTimeout;
            
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.performSearch(e.target.value);
                }, 300);
            });
        }
    }
    
    performSearch(query) {
        if (query.length < 2) return;
        
        // Simple search implementation
        console.log('Searching for:', query);
        // Implement actual search logic here
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Initialize advanced navigation
    window.advancedNavigation = new AdvancedNavigation();
    window.menuEnhancements = new MenuEnhancements();
    
    // Add CSS for ripple effect
    const style = document.createElement('style');
    style.textContent = `
        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }
        
        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { AdvancedNavigation, MenuEnhancements };
}
