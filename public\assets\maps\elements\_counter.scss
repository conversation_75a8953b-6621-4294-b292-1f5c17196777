.counter-bg {
    background-image: url(../images/counterup/01.webp);
    height: 762px;
    background-size: cover;
    background-repeat: no-repeat;

    @media #{$mdsm-layout} {
        height: auto;
    }

    .single-counter {
        display: flex;
        align-items: center;
        gap: 25px;

        @media #{$sm-layout} {
            justify-content: flex-start !important;
            padding-left: 0 !important;
            margin: 15px 0;
        }

        .icon {
            height: 90px;
            min-width: 90px;
            border-radius: 50%;
            background: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .counter-details .title {
            color: #fff;
            margin-bottom: -2px;
            font-weight: 700;
        }

        .counter-details p {
            color: #fff;
            font-size: 14px;
            font-weight: 500;
            text-transform: uppercase;
            @media #{$laptop-device} {
                font-size: 14px;
            }
        }
    }
}


.counter-main-wrapper-area-start-6 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 1px solid #20282d21;

    @media #{$smlg-device} {
        flex-wrap: wrap;

    }

    .single-counter-area-main {
        padding: 90px 55px 73px 55px;
        border-right: 1px solid #20282d21;

        @media #{$smlg-device} {
            border: none;
        }

        @media #{$sm-layout} {
            padding: 25px;
        }

        .title {
            font-size: 70px;
            margin-bottom: 3px;

            @media #{$large-mobile} {
                font-size: 32px;
            }
        }

        span.bottom {
            font-weight: 700;
            font-size: 16px;
            text-transform: uppercase;
            color: #1C2539;
        }

        &:last-child {
            border: none;
        }
    }
}

.rts-counter-up-area-eight {
    position: absolute;
    bottom: -180px;
    left: 50%;
    transform: translateX(-50%);

    @media #{$smlg-device} {
        width: 100%;
    }

    @media #{$laptop-device} {
        width: 100%;
    }
}

.counter-up-area-eight-banner-bottom {
    padding: 94px 62px;
    border-radius: 40px;
    background: var(--color-primary);
    width: 1290px;
    margin: auto;
    height: 326px;
    display: flex;
    align-items: center;
    gap: 90px;

    @media #{$laptop-device} {
        width: 100%;
    }

    @media #{$smlg-device} {
        width: 100%;
    }

    @media #{$sm-layout} {
        // flex-direction: column;
        // align-items: flex-start;
        gap: 30px;
        padding: 25px;
    }

    .absolute-image {
        position: absolute;
        right: 40px;
        bottom: 0;

        @media #{$smlg-device} {
            max-width: 31%;
        }
        @media #{$mdsm-layout} {
            max-width: 23%;
        }
        @media #{$sm-layout} {
            display: none;
        }
    }
}

.single-counter-area-eight {
    .title {
        color: #fff;
    }

    span.business {
        text-transform: uppercase;
        color: #B3B7C1;
        font-size: 24px;
        br{
            display: block;
        }
        @media #{$large-mobile} {
            font-size: 16px;
        }
    }

    .star-icon {
        margin-top: 20px;

        i {
            color: #B3B7C1;
            font-size: 22px;
        }
    }
}